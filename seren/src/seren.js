import { logger } from './shared/logger-client.js';
import {combineMessages} from './shared/message-combiner.js';
import { sendObservationsToServer } from './reef.js';
function sleep(ms) { return new Promise(r => setTimeout(r, ms)); }
import { SerenConfig } from './seren/seren-conf';
import { SerenCycles } from './seren/seren-cycles';
import { RedditTabManager } from './reef/rc-tab-dispatcher';
import { InstructionsProcessor } from './seren/instructions-processor';
import { DuxConnector } from './reef/dux-connector.js';

class LockManager {
  constructor() {
    this.locks = new Map();
  }

  async acquireLock(lockName) {
    while (this.locks.has(lockName)) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    this.locks.set(lockName, true);
  }

  releaseLock(lockName) {
    this.locks.delete(lockName);
  }

  isLocked(lockName) {
    return this.locks.has(lockName);
  }
}

export class Seren {
  // Storage keys for persisting times
  static STORAGE_KEY_START_TIME = 'seren.startTime';
  static STORAGE_KEY_STOP_TIME = 'seren.stopTime';

  constructor(override = false) {
    this.account_id = null;
    this.lastInstructionCheck = null;
    this.lastRunTime = Date.now();
    this.intervalId = null;
    this.tabManager = new RedditTabManager();
    this.serenConfig = new SerenConfig();
    this.config = this.serenConfig.getAll();
    this.cycles = new SerenCycles(this);
    this.instructionProcessor = new InstructionsProcessor(this.config, this.tabManager);
    this.lockManager = new LockManager();

    this.startTime = null;
    this.stopTime = null;

    this.loadTimesFromStorage().catch(error => {
      console.error('Failed to load times from storage:', error);
      logger('Seren', `Failed to load times from storage: ${error.message}`, 'error');
    });
  }

  async checkActiveRooms() {
    const serenName = this.account_id;
    const tabId = this.tabManager.currentChatTabId;
    if (!tabId) {
      logger('Seren', 'No current chat tab ID found for checking active rooms', 'warn');
      return [];
    }
    try {
      const active_rooms = await chrome.scripting.executeScript({
        target: { tabId },
        func: (serenName) => (new window.rc_rooms.RoomsQuery()).get_active_rooms(serenName),
        args: [serenName]
      });

      const { acceptedInvitations = [] } = await chrome.storage.local.get('acceptedInvitations')
      const tenMinutesAgo = new Date().getTime() - (10 * 60 * 1000)
      const activeRooms = active_rooms[0]?.result || []

      acceptedInvitations.forEach(invite => {
        if (new Date(invite.timestamp).getTime() > tenMinutesAgo) {
          const existingRoom = activeRooms.find(room => room.sender_name === invite.senderName) 
          if (!existingRoom) {
            activeRooms.push({
              sender_name: invite.senderName,
              timestamp: invite.timestamp
            })
          }
        }
      })
      this.lastKnownActiveRoomsCount = activeRooms.length
      return activeRooms;
    } catch (error) {
      logger('Seren', `Error during checkActiveRooms: ${error.message}`, 'error');
      return [];
    }
  }
  async acceptInvites(request) {
    let tabId;
    try {
      //await this.tabManager.activateTab();
      tabId = await this.tabManager.currentTabId;
      if (!tabId) return { success: false, error: 'No Reddit Chat tab found' };

      const targetCount = request.count || 1;
      let invitesAccepted = 0, failures = 0, lastResult, lastMessage, totalSentCount = 0;
      const serenName = this.account_id;
      const acceptedInvites = [];

      const acquireLock = (await chrome.scripting.executeScript({
        target: { tabId },
        func: () => window.actionsModule.acquireLock()
      }))[0]?.result

      if (acquireLock) {
        logger('Seren.Rooms', 'Starting to accept invites', 'info');
      }
      else{
        logger('Seren.Rooms', 'Page is locked for instructions executions, skip this wave','info')
        return
      }

      while (invitesAccepted < targetCount && failures < 1) {
      const roomInfo = (await chrome.scripting.executeScript({
        target: { tabId },
        func: (serenName) => window.rc_requests?.actions?.getFirstRequestRoom(serenName),
        args: [serenName]
      }))[0]?.result
      if (!roomInfo.success) {
        return { success: false, error: 'No requests found' };
      }
      const senderName = roomInfo.senderName
     
      const fishStatus = await DuxConnector.checkFishStatus(this.config.duxEndpoint,
          this.config.serenKey, senderName)
      const needAccept = fishStatus < 1
      
      if (!needAccept) {
        logger('Reef.AcceptInvites', `Skipping invite from ${senderName}`, 'info')
       // continue;
      }
      
      
      const result = (await chrome.scripting.executeScript({
        target: { tabId },
        func: (roomInfo, needAccept) => window.rc_requests?.actions?.processInvites(roomInfo, needAccept),
        args: [roomInfo, needAccept],
      }))[0]?.result
      
      const isSuccess = result?.success
      if (!isSuccess) {
        logger('Reef.AcceptInvites', `Failure: ${result?.error || 'Unknown error'}`, 'warn')
        lastResult = result
        failures++
      } else if (result.roomData) {
        failures = 0
        if (!needAccept) {
          await sleep(1000)
          continue
        }
        invitesAccepted++
        lastResult = result
        
        // Add to accepted invites if not already present
        const existingInvite = acceptedInvites.find(invite => invite.senderName === senderName);
        if (!existingInvite) {
          acceptedInvites.push({
            senderName,
            timestamp: new Date().toISOString()
          });
        }
        
        lastMessage = combineMessages(result.roomData.messages);
        if (lastMessage.length > 0) {
          const extraIds = result.roomData.messages.map(msg => msg.message_id)
          totalSentCount += await sendObservationsToServer(this.config, lastMessage, extraIds)
          logger('Reef.RoomObservations', `Sent ${lastMessage[0].user_name} message: "${lastMessage[0].message_text}"`, 'success', lastMessage)
        } else {
          logger('Reef.RoomObservations', `User did not send any text`, 'warn', lastMessage)
        }
      } else {
        lastResult = result
        logger('Reef.AcceptInvites', 'No invites accepted this attempt', 'warn')
        failures++
      }

      await sleep(1500)
    }

    if (failures >= 1 && invitesAccepted < targetCount) {
      logger('Reef.AcceptInvites', `Stopped after ${invitesAccepted} invites due to failures`, 'warn')
    }

      // Save accepted invites to storage if any were accepted
      if (acceptedInvites.length > 0) {
        const { acceptedInvitations = [] } = await chrome.storage.local.get('acceptedInvitations');
        await chrome.storage.local.set({
          acceptedInvitations: [...acceptedInvitations, ...acceptedInvites],
          lastInviteTime: new Date().toISOString()
        });
        logger('Reef.AcceptInvites', `Saved ${acceptedInvites.length} invites to storage`);
      }

      return {
        success: invitesAccepted > 0,
        roomId: lastResult?.roomId,
        error: lastResult?.error,
        lastMessage,
        invitesAccepted,
        sentCount: totalSentCount,
        message: invitesAccepted ? `Successfully accepted ${invitesAccepted} request(s)` : 'No requests were accepted',
        acceptedInvites
      };
    } catch (error) {
      logger('Seren.Rooms', `Error in acceptInvites: ${error.message}`, 'error');
      return { success: false, error: error.message };
    } finally {
      // Release the browser-side lock
      if (tabId) { // Only attempt if tabId is valid
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            func: () => window.actionsModule.releaseLock()
          });
        } catch (browserUnlockError) {
          logger('Seren.Rooms', `Failed to release browser-side lock: ${browserUnlockError.message}`, 'error');
        }
      }

      // Release the background script lock
      try {
        // this.lockManager.releaseLock('message_processing');
      } catch (backgroundUnlockError) {
        logger('Seren.Rooms', `Failed to release background script lock: ${backgroundUnlockError.message}`, 'error');
      }
    }
  }
  
  async loadTimesFromStorage() {
    try {
      const result = await chrome.storage.local.get([
        Seren.STORAGE_KEY_START_TIME,
        Seren.STORAGE_KEY_STOP_TIME
      ]);

      if (result[Seren.STORAGE_KEY_START_TIME]) {
        this.startTime = result[Seren.STORAGE_KEY_START_TIME];
        console.log('Loaded startTime from storage:', this.startTime, 'at', new Date(this.startTime).toLocaleTimeString());
      }

      if (result[Seren.STORAGE_KEY_STOP_TIME]) {
        this.stopTime = result[Seren.STORAGE_KEY_STOP_TIME];
        console.log('Loaded stopTime from storage:', this.stopTime, 'at', new Date(this.stopTime).toLocaleTimeString());
      }
    } catch (error) {
      console.error('Error loading times from storage:', error);
      logger('Seren', `Error loading times from storage: ${error.message}`, 'error');
    }
  }
  async getNextScheduledTimes() {
    const now = Date.now();
   // console.log('getNextScheduledTimes called at:', new Date(now).toISOString());

    // Ensure we have the latest times from storage
    await this.refreshTimesFromStorage();

    // console.log('Current state:', {
    //   intervalId: this.intervalId,
    //   startTime: this.startTime ? new Date(this.startTime).toISOString() : null,
    //   stopTime: this.stopTime ? new Date(this.stopTime).toISOString() : null
    // });

    // Calculate next service check time
    const nextServiceCheck = this.intervalId
      ? (this.lastRunTime + this.config.serviceInterval)
      : null;

    // Calculate next instruction check time
    const nextInstructionCheck = this.intervalId
      ? (this.lastInstructionCheck + this.config.instructionsInterval || now + this.config.instructionsInterval)
      : null;

    // Calculate running time or time since stopped
    const isRunning = !!this.intervalId;
    const runningTime = isRunning && this.startTime ? (now - this.startTime) : null;
    const stoppedTime = !isRunning && this.stopTime ? (now - this.stopTime) : null;

    // console.log('Calculated timing values:', {
    //   isRunning,
    //   runningTime,
    //   stoppedTime,
    //   nextServiceCheck: nextServiceCheck ? new Date(nextServiceCheck).toISOString() : null,
    //   nextInstructionCheck: nextInstructionCheck ? new Date(nextInstructionCheck).toISOString() : null
    // });

    return {
      nextServiceCheck,
      nextInstructionCheck,
      runningTime,
      stoppedTime,
      startTime: this.startTime,
      stopTime: this.stopTime
    };
  }


  async refreshTimesFromStorage() {
    try {
      const result = await chrome.storage.local.get([
        Seren.STORAGE_KEY_START_TIME,
        Seren.STORAGE_KEY_STOP_TIME
      ]);

      if (result[Seren.STORAGE_KEY_START_TIME]) {
        this.startTime = result[Seren.STORAGE_KEY_START_TIME];
      }

      if (result[Seren.STORAGE_KEY_STOP_TIME]) {
        this.stopTime = result[Seren.STORAGE_KEY_STOP_TIME];
      }
    } catch (error) {
      console.error('Error refreshing times from storage:', error);
    }
  }

  async getRedditChatWindow() {
    return await this.tabManager.getRedditChatWindow();
  }

  async executeInstructions()
  {
    try {
      await this.getRedditChatWindow();
      await this.activateChatPage(true);
      await this.instructionProcessor.retrieveInstructions(this.account_id)
      this.lastInstructionCheck = Date.now()
      await this.instructionProcessor.executeInstructions()
    }
    catch (error) {
      console.error('Error executing instructions:', error);
    }
  }

  timeout(ms) {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms);
    });
  }

  async run() {
    if (this.intervalId) {
      return;
    }

    this.lastActionDate = null;
    await this.activateChatPage(true);
    this.config = (await SerenConfig.getConfig()).getAll();

    // Set the start time
    this.startTime = Date.now();
    this.stopTime = null;
    console.log('Setting startTime:', this.startTime, 'at', new Date(this.startTime).toLocaleTimeString());
    logger('Seren', `Started at ${new Date(this.startTime).toLocaleTimeString()}. First wave will start in 5 seconds`, 'info');

    // Save start time to storage and clear stop time
    try {
      await chrome.storage.local.set({
        [Seren.STORAGE_KEY_START_TIME]: this.startTime,
        [Seren.STORAGE_KEY_STOP_TIME]: null // Clear stop time when starting
      });
      console.log('Saved startTime to storage:', this.startTime);
    } catch (error) {
      console.error('Error saving startTime to storage:', error);
      logger('Seren', `Error saving startTime to storage: ${error.message}`, 'error');
    }

    // Start the first wave after 5 seconds
    setTimeout(() => {
      this.cycles.runCycle();
    }, 5000);

    // Set up regular interval for subsequent waves
    this.intervalId = setInterval(() => this.cycles.runCycle(), this.config.serviceInterval);
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      this.lastInstructionCheck = null;
      this.lastActionDate = null;

      // Set the stop time
      this.stopTime = Date.now();
      this.lastStopTime = this.stopTime;
      console.log('Setting stopTime:', this.stopTime, 'at', new Date(this.stopTime).toLocaleTimeString());

      // Save stop time to storage and clear start time
      try {
        chrome.storage.local.set({
          [Seren.STORAGE_KEY_STOP_TIME]: this.stopTime,
          [Seren.STORAGE_KEY_START_TIME]: null // Clear start time when stopped
        });
        console.log('Saved stopTime to storage:', this.stopTime);
      } catch (error) {
        console.error('Error saving stopTime to storage:', error);
        logger('Seren', `Error saving stopTime to storage: ${error.message}`, 'error');
      }

      // Calculate runtime
      const runTime = this.startTime ? Math.floor((this.stopTime - this.startTime) / 1000) : 0;
      const minutes = Math.floor(runTime / 60);
      const seconds = runTime % 60;
      const timeString = `${minutes}m ${seconds}s`;

      console.log('Calculated runtime:', { runTime, minutes, seconds, timeString });
      logger('Seren', `Stopped after running for ${timeString}`, 'warn');
    }
  }

  async runServiceCycle() {
    try {
      logger('Run', 'Manual service cycle initiated');
      this.lastRunTime = Date.now();
      await this.cycles.runCycle();
      logger('Run', 'Manual service cycle completed successfully');
      return true;
    } catch (error) {
      // Even when there's an error, update lastRunTime to ensure next cycle is scheduled correctly
      this.lastRunTime = Date.now();
      logger('Run', `Manual service cycle failed: ${error}`, 'error');
      throw error;
    }
  }

  async broadcastUIUpdate(additionalData = {}) {
    const times = await this.getNextScheduledTimes();
    return {
      nextServiceCheck: times.nextServiceCheck,
      nextInstructionCheck: times.nextInstructionCheck,
      config: this.config,
      ...additionalData
    };
  }

  async getServiceStatusWithOperations() {
    //console.log('getServiceStatusWithOperations called');
    const uiData = await this.broadcastUIUpdate();
    const isRunning = !!this.intervalId;
    const times = await this.getNextScheduledTimes();

    // console.log('Times from getNextScheduledTimes:', times);

    const result = {
      ...uiData,
      status: isRunning ? 'running' : 'stopped',
      startTime: times.startTime,
      stopTime: times.stopTime,
      runningTime: times.runningTime,
      stoppedTime: times.stoppedTime
    };

    // console.log('getServiceStatusWithOperations returning:', result);
    return result;
  }

  async checkChatPageStatus() {
    try {
      const tab = await this.getRedditChatWindow();

      const result = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          try {
            if (window.rc_page?.RCPage) {
              const isReady = window.rc_page.RCPage.isPageReady();
              const accountId = window.rc_page.RCPage.accountId ||
                               window.rc_page.RCPage.getCurrentAccountId();
              return {
                isReady,
                accountId,
                message: isReady ? 'Chat page is ready' : 'Chat page is not ready'
              };
            } else {
              return { isReady: false, message: 'RCPage script not found' };
            }
          } catch (error) {
            return { isReady: false, message: `Error checking chat page: ${error.message}` };
          }
        }
      });

      const status = result[0]?.result || { isReady: false, message: 'Execution failed' };

      // Update account_name from accountId if available
      if (status.accountId) {
        this.account_id = status.accountId;
      }

      return status;
    } catch (error) {
      logger('Seren.CheckChatPageStatus', `Error: ${error.message}`, 'error');
      return { isReady: false, message: `Error: ${error.message}` };
    }
  }

  async reloadChatPage() {
    await this.tabManager.getRedditChatWindow();
    const tab = await this.tabManager.activateTab(false);
    const tabId = this.tabManager.currentChatTabId
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: async () => {
        try {
          if (window.rc_page?.RCPage) {
            return await window.rc_page.RCPage.activateChatPage();
          } else {
            // If module not found, do a manual reload
            location.reload();
            return { success: false, message: 'RCPage module not found, page reloaded' };
          }
        } catch (error) {
          return { success: false, message: `Error reloading chat page: ${error.message}` };
        }
      }
    }).catch(async error => {
      logger('Service', `Script execution failed: ${error.message}`, 'error');
      return [{ result: { success: true, message: 'Fallback reload executed' } }];
    });

    const reloadResult = result[0]?.result || { success: false, message: 'Execution failed' };
    // logger('Service', reloadResult.message, reloadResult.success ? 'success' : 'error');

    return reloadResult;
  }

  async activateChatPage(force = false) {
    await this.tabManager.activateTab(force);

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      const status = await this.checkChatPageStatus();

      if (status.isReady) {
        // The checkChatPageStatus method already updates this.account_name if accountId is available
        this.account_id = status.accountId;
        return {
          success: true,
          message: 'Chat page successfully activated',
          accountId: status.accountId
        };
      }

      await delay(3000);
      attempts++;
    }

    return { success: false, message: 'Failed to activate chat page after multiple attempts' };
  }

  async reloadAndActivateChatPage() {
    await this.reloadChatPage();
    return await this.activateChatPage();
  }
}
