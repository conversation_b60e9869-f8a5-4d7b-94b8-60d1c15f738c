if (!window.rc_actions) {

  class ContentMediaClient {
    static async getMedia(key) {
      // Wrap sendMessage in a Promise so we can await it
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
            { action: 'getMedia', key },
            (res) => {
              if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
              }
              resolve(res);
            }
        );
      });

      // Check for success / error
      if (!response.success) {
        throw new Error(response.error);
      }
      return response.blob;
    }
  }
  class ChatActions {
  static _locked = false
  static acquireLock() {
    if (this._locked) return false
    this._locked = true
    return true
  }
  static releaseLock() {
    this._locked = false
  }
    constructor() {
      this.instructionsStorage = window.storageModule?.instructionStorage || null;
      this.abilities = window.abilities || null;
      this.elements = this.abilities.elements || null;
      this.isTabVisible = !document.hidden;
      this.lastTabVisibilityChange = Date.now();
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
      this.pendingSends = new Map();
    }
    _hashMessage(room_id, messageText) {
      return `${room_id}_${messageText.slice(0, 50)}`;
    }
    async log(operation, message, level = 'log',details) {
      try {
        if (chrome && chrome.runtime && !chrome.runtime.lastError) {
          await chrome.runtime.sendMessage({ action: 'LOG', operation, message, level, details });
        }
      } catch (_) {
        // Silent failure - logging should never break functionality
      }
    }
    async handleVisibilityChange() {
      const isNowVisible = !document.hidden;
      const now = Date.now();
      const visibilityChangeDelay = now - this.lastTabVisibilityChange;
      const formattedDelay = (visibilityChangeDelay / 1000).toFixed(2);
      await this.log('RC-Space.PageState', `Chat Page visibility changed: ${this.isTabVisible} -> ${isNowVisible}, after ${formattedDelay}`);
      if (!this.isTabVisible && isNowVisible) {
        if (visibilityChangeDelay > 100000) {
          await this.log('RC-Space.Actions', 'Chat Page was inactive for extended period','warn',{visibilityChangeDelay});
        }
      }
      this.isTabVisible = isNowVisible;
      this.lastTabVisibilityChange = now;
    }

    async wait(seconds) {
      return window.clockModule?.instance?.wait(seconds);
    }

    async scrollAndFindRoom(roomName) {
      try {
        const roomsNav = this.elements.roomsNav();
        const virtualScroll = roomsNav.querySelector('rs-virtual-scroll');
        const scrollableElement = virtualScroll;
        this.abilities.scroll.toTop();
        await this.wait(2);

        const roomHeight = 80;
        const roomsPerPage = Math.floor(scrollableElement.clientHeight / roomHeight) || 1;
        const pageIncrement = roomsPerPage * roomHeight;
        const maxPageScrollAttempts = 1000; // Increased max attempts
        let pageScrollAttempts = 0;
        const totalHeight = scrollableElement.scrollHeight;
        let targetRoom = null;

        // Prepare encoded room id for URL comparison
        const encodedRoomId = encodeURIComponent(roomName);

        while (scrollableElement.scrollTop < totalHeight && pageScrollAttempts < maxPageScrollAttempts) {
          targetRoom = virtualScroll.shadowRoot.querySelector(`rs-rooms-nav-room[room="${roomName}"]`);
          if (targetRoom) {
            const navButton = targetRoom.shadowRoot?.querySelector('a');
            if (navButton) {
              // Save URL before clicking
              const oldUrl = window.location.href;
              navButton.click();

              // Wait for URL to match expected pattern
              const urlPattern = new RegExp(`https://chat.reddit.com/room/(${encodedRoomId})`);
              let found = false;
              let tries = 0;
              // Try up to 20 times with 200ms wait (total 4 seconds)
              while (tries < 20) {
                await this.wait(0.2);
                const newUrl = window.location.href;
                if (urlPattern.test(newUrl)) {
                  found = true;
                  break;
                }
                tries++;
              }
              if (!found) {
                await this.log('Sailor.ScrollAndFindRoom', `URL did not update to expected room: "${roomName}", got: ${window.location.href}`, 'warn');
                return null;
              }
              await this.wait(0.5);
              return targetRoom;
            }
          }
          const previousScrollTop = scrollableElement.scrollTop;
          scrollableElement.scrollTop += pageIncrement;
          await this.wait(0.3);
          pageScrollAttempts++;
          if (previousScrollTop === scrollableElement.scrollTop) {
            await this.log('Sailor.ScrollAndFindRoom', 'Reached end of scroll during page scrolling', 'info');
            break;
          }
        }
        scrollableElement.scrollTop = totalHeight;
        await this.wait(1);
        targetRoom = virtualScroll.shadowRoot.querySelector(`rs-rooms-nav-room[room="${roomName}"]`);
        if (targetRoom) {
          const navButton = targetRoom.shadowRoot?.querySelector('a');
          if (navButton) {
            // Save URL before clicking
            const oldUrl = window.location.href;
            navButton.click();
            // Wait for URL to match expected pattern
            const urlPattern = new RegExp(`/room/(${encodedRoomId})$`);
            let found = false;
            let tries = 0;
            while (tries < 20) {
              await this.wait(0.2);
              const newUrl = window.location.href;
              if (newUrl !== oldUrl && urlPattern.test(newUrl)) {
                found = true;
                break;
              }
              tries++;
            }
            if (!found) {
              await this.log('Sailor.ScrollAndFindRoom', `URL did not update to expected room: "${roomName}", got: ${window.location.href}`, 'warn');
              return null;
            }
            await this.wait(0.5);
            await this.log('Sailor.ScrollAndFindRoom', `Room found after scrolling to bottom: ${roomName}`, 'success');
            return targetRoom;
          }
        }
        await this.log('Sailor.ScrollAndFindRoom', `Room not found: ${roomName}`, 'error');
        return null;
      } catch (error) {
        await this.log('Sailor.scrollAndFindRoom', `Error finding room: ${error.message}`, 'error');
        return null;
      }
    }
    
    async isMessageAlreadySentInDb(action, needLog = true){
      const RoomsQuery = new window.rc_rooms.RoomsQuery();
      const sentInDb = await RoomsQuery.check_message_is_sent(action.room_id, action);
      if (sentInDb) {
        if (needLog) {
          const preview = action.content.text.value.trim().substring(0, 30);
          await this.log('Sailor.ExecuteActions', `Duplicate detected via DB: "${preview}..."`, 'warn');
        }
        return true;
      }
    }
    async isMessageAlreadySent(action, needLog = true) {
      try {
        if (!action || !action.room_id || !action.content || !action.content.text || typeof action.content.text.value !== 'string') {
          // Invalid action object; treat as not sent.
          return false;
        }
        const messageText = action.content.text.value.trim();
        const pendingKey = this._hashMessage(action.room_id, messageText);
        if (this.pendingSends.has(pendingKey)) {
          if (needLog) {
            await this.log('Sailor.ExecuteActions', `Duplicate detected via pendingSends: "${messageText.slice(0,30)}..."`, 'warn');
          }
          return true;
        }
        // 1. Database-level check for duplication.
       if (await this.isMessageAlreadySentInDb(action,needLog))
         return true;

        // 2. UI-level check against the last message in the chat.
        const lastMessage = this.getLastMessage();
        const normalizeText = (txt) =>
            txt
                .replace(/[\s\n]+/g, ' ') // collapse spaces/newlines
                .trim()
                .toLowerCase();
        if (
            lastMessage &&
            lastMessage.content &&
            lastMessage.content.text &&
            typeof lastMessage.content.text.value === 'string'
        ) {
          const messageToSend = action.content.text.value;
          const lastMessageText = lastMessage.content.text.value;

          const normSend = normalizeText(messageToSend);
          const normLast = normalizeText(lastMessageText);

          // Option 1: check if one contains the other and they're both reasonably long
          if (
              normSend.length > 10 && // avoid matching empty/short junk
              normLast.length > 10 &&
              (
                  normSend.includes(normLast) ||
                  normLast.includes(normSend)
              )
          ) {
            if (needLog) {
              await this.log(
                  'Sailor.ExecuteActions',
                  `Duplicate detected (normalized match) on page: "${normSend.slice(0, 30)}..."`,
                  'warn'
              );
            }
            return true;
          }
        }

        return false;
      } catch (error) {
        // Log unexpected errors and treat as not already sent to allow retry.
        await this.log('Sailor.ExecuteActions', `Error in isMessageAlreadySent: ${error.message}`, 'error');
        return false;
      }
    }

    async execActions() {
      let instructions = await this.instructionsStorage.getInstructionsQueue();
      if (!instructions.length) return;
      // Deduplicate: Map by room_id + normalized message text
      const seen = new Set();
      const deduped = [];
      const toRemove = [];

      for (const action of instructions) {
        if (action.content?.text?.value && action.room_id) {
          const normText = action.content.text.value.trim();
          const key = `${action.room_id}___${normText}`;
          if (seen.has(key)) {
            toRemove.push(action.id);
            continue;
          }
          seen.add(key);
        }
        deduped.push(action);
      }

      for (const id of toRemove) {
        await this.instructionsStorage.markAsProcessed(id, 'duplicate_in_queue');
      }

      instructions = deduped;
      await this.log('Sailor.Queue', `Queued for execution ${instructions.length} instructions`,'info',{messages:instructions});
      if (!window.actionsModule.acquireLock()) {
        return {result: "Actions queued for execution"};
      }
      while (instructions.length) {
        try {
          const action = instructions.pop();
          if (await this.isMessageAlreadySentInDb(action,true))
          {
            await this.instructionsStorage.markAsProcessed(action.id,'already_sent');
            continue;
          }
          if (!await this.scrollAndFindRoom(action.room_id)) {
            await this.instructionsStorage.markAsProcessed(action.id,'room_not_found');
            continue;
          }
          await this.wait(1);
          if (action.content?.media?.storageKey) {
            const base64Data = await ContentMediaClient.getMedia(action.content.media.storageKey);
            //const base64Data = URL.createObjectURL(blob);
            await this.sendFile(base64Data, action.content.text?.value);
            await this.instructionsStorage.markAsProcessed(action.id,'file_sent');
          }
          else if (action.content?.text?.value) {
            const messageText = action.content.text.value.trim(); // PATCH
            const pendingKey = this._hashMessage(action.room_id, messageText); // PATCH

            if (await this.isMessageAlreadySent(action)) {
              await this.instructionsStorage.markAsProcessed(action.id, 'already_sent');
              continue;
            }

            this.pendingSends.set(pendingKey, Date.now()); // PATCH

            try { // PATCH
              const skipSending = action.is_simulation
              const sendResult = await this.sendMessage(messageText,skipSending);
              if (!sendResult.success) {
                //await this.instructionsStorage.markAsProcessed(action.id, 'send_error');
                this.pendingSends.delete(pendingKey);
                continue; // Skip this action, or handle as needed
              }
              if (!action.is_simulation) {
                await this.waitForMessageInDOM(action.room_id, messageText);
                if (await this.isMessageAlreadySent(action, false)) {
                  await this.instructionsStorage.markAsProcessed(action.id, 'verified');
                }
              }
              else {
                await this.instructionsStorage.markAsProcessed(action.id, 'verified');
              }
            } finally { // PATCH: always clear pending
              this.pendingSends.delete(pendingKey);
            }
          }
        }
        catch (error) {
          await this.log('Sailor.ExecuteActions', `Action failed: ${error.message}`, 'error');
          
        }
      }
      window.actionsModule.releaseLock();
      
    }
    async waitForMessageInDOM(room_id, messageText, timeout = 4000, interval = 300) {
      const start = Date.now();
      let last = '';
      while (Date.now() - start < timeout) {
        // Optionally, re-navigate to room in case of UI lag
        const lastMsg = this.getLastMessage();
        if (
            lastMsg &&
            lastMsg.content &&
            lastMsg.content.text &&
            typeof lastMsg.content.text.value === 'string'
        ) {
          last = lastMsg.content.text.value.trim();
          if (last === messageText.trim()) {
            return true;
          }
        }
        await this.wait(interval / 1000); // Convert ms to s
      }
      await this.log('Sailor.WaitForMessage', `Timeout waiting for "${messageText.slice(0, 30)}..." in DOM. Last found: "${last.slice(0, 30)}..."`, 'warn');
      return false;
    }
    async triggerQueue(){
      setTimeout(() => this.execActions(), 0);
    }
    async navigateToRoom(roomId) {
        try {
          const room = await this.scrollAndFindRoom(roomId);
          if (!room) {
            await this.log('Sailor.NavigateToRoom', 'Room not found', 'error');
            return false;
          }
          return await this.wait(2)
        } catch (error) {
          await this.log('Sailor.NavigateToRoom', `Navigation failed: ${error.message}`, 'error');
          return false;
        }
    }
    async sendMessage(text, skipSending = false) {
      try {
        await this.wait(0.5);
        const isTyped = await window.abilities.type.simulateTyping(this.elements.messageInput(), text);
        await this.log('Sailor.SendMessage', `Message: ${text}`);

        // Check for send error after click & wait
        if (this.hasSendErrorForLastMessage()) {
          await this.log('Sailor.SendMessage', `Send error detected for message: ${text}`, 'error');
          return { success: false, error: 'Unable to send message' };
        }
        return { success: true };
      } catch (error) {
        await this.log('Sailor', `Error sending message: ${error}`, 'error');
        return { success: false, error: error.message };
      }
    }

    async sendFile(fileData, text) {
      try {
        const dropTarget = this.elements.roomOverlay().querySelector("main");
        let file;
        if (typeof fileData === 'string') {
          file = this.abilities.media.base64ToFile(fileData);
        } else {
          file = fileData;
        }
        //rs-message-composer-attachments
        const target = this.elements.composer().querySelector('rs-message-composer-attachments').shadowRoot.querySelector('input[type="file"]')
        //await this.abilities.attachments.pickAndAttach(target,fileData);
        await this.abilities.attachments.attachFile(target,fileData)
        //media.simulateFileDrop(dropTarget, file)
        await this.wait(1.5)
        if (text) {
          await this.sendMessage(text)
        }
        this.elements.submitButton().click()
        await this.log('Sailor.SendFile', `Sent file ${text ? 'with text ' + text : ''}`,text);
        return true;
      } catch (error) {
        await this.log('Sailor.SendFile', `Error sending file: ${error.message}`, 'error');
        return false;
      }
    }

    getLastMessage() {
      // Extract time from the <time> element inside <faceplate-date>
      const roomChat = this.elements.roomOverlay()
      const timeline = roomChat.querySelector('main rs-timeline').shadowRoot
      const msgEl = timeline.querySelector('rs-virtual-scroll-dynamic').shadowRoot.querySelector('rs-timeline-event[last-live-item]').shadowRoot

      const timeEl = msgEl.querySelector('faceplate-date time');
      const time = timeEl ? timeEl.getAttribute('datetime') : '';

      const senderEl = msgEl.querySelector('.user-name');
      const sender = senderEl ? senderEl.textContent.trim() : '';

      const textEl = msgEl.querySelector('.room-message-text');
      const text = textEl ? textEl.textContent.trim() : '';
      const messageId = msgEl.host.getAttribute('data-id') || '';

      return {
        id: messageId,
        message_id: messageId,
        message_time: new Date(time).toISOString(),
        time: new Date(time).toISOString(),
        user_name: sender,
        user_id:sender,
        message_text:text,
        content:{
          type:"text",
          text:{value:text}
        }
      };
    }

    hasSendErrorForLastMessage() {
      try {
        const roomChat = this.elements.roomOverlay();
        const timeline = roomChat.querySelector('main rs-timeline').shadowRoot;
        const lastMsg = timeline.querySelector('rs-virtual-scroll-dynamic').shadowRoot.querySelector('rs-timeline-event[last-live-item]').shadowRoot;
        // The .room-message-body contains error div if present
        const msgBody = lastMsg.querySelector('.room-message-body');
        if (!msgBody) return false;
        const errorDiv = msgBody.querySelector('.error');
        if (errorDiv && errorDiv.textContent.includes('Unable to send message')) {
          return true;
        }
        return false;
      } catch (_) {
        return false;
      }
    }
  }
  window.rc_actions = { ChatActions: new ChatActions(), acquireLock : ChatActions.acquireLock, releaseLock: ChatActions.releaseLock };
}
